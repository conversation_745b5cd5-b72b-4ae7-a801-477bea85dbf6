<?php

namespace App\Controllers;

use App\Models\applicantsModel;
use App\Models\ApplicantsExperiencesModel;
use App\Models\ApplicantEducationModel;
use App\Models\EducationModel;
use App\Models\ApplicantFilesModel;

class ApplicantApplicationController extends BaseController
{
    protected $applicantsModel;
    protected $experiencesModel;
    protected $applicantEducationModel;
    protected $educationModel;
    protected $applicantFilesModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
        $this->applicantsModel = new applicantsModel();
        $this->experiencesModel = new ApplicantsExperiencesModel();
        $this->applicantEducationModel = new ApplicantEducationModel();
        $this->educationModel = new EducationModel();
        $this->applicantFilesModel = new ApplicantFilesModel();
    }

    public function uploadFile()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $file = $this->request->getFile('file');
        $title = $this->request->getPost('file_title');
        $description = $this->request->getPost('file_description');

        if (!$file || !$file->isValid() || $file->hasMoved()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid file upload'
            ]);
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'uploads/applicant_files';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        try {
            // Generate unique filename
            $newName = $applicant_id . '_' . time() . '_' . $file->getRandomName();

            // Move file to uploads directory
            if ($file->move($uploadPath, $newName)) {
                // Save file information to database
                $fileData = [
                    'applicant_id' => $applicant_id,
                    'title' => $title,
                    'description' => $description,
                    'file_path' => 'public/uploads/applicant_files/' . $newName,
                    'file_name' => $file->getName(),
                    'file_size' => $file->getSize(),
                    'file_type' => $file->getMimeType(),
                    'created_by' => $applicant_id,
                    'updated_by' => $applicant_id
                ];

                $this->applicantFilesModel->insert($fileData);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'File uploaded successfully',
                    'file' => $fileData
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error moving uploaded file'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error uploading file: ' . $e->getMessage()
            ]);
        }
    }

    public function deleteFile($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        // For UI development - simulate ownership verification
        if (!$id) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'File not found or access denied'
            ]);
        }

        // For UI development - simulate successful file deletion
        return $this->response->setJSON([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
    }

    public function viewApplication($applicationId)
    {
        // Get current applicant ID from session
        $applicant_id = session()->get('applicant_id');

        // Using dummy data for UI development
        $application = [
            'id' => $applicationId,
            'applicant_id' => $applicant_id,
            'position_id' => 1,
            'status' => 'pending',
            'applied_date' => '2024-01-15',
            'cover_letter' => 'This is a sample cover letter for the application.'
        ];

        $position = [
            'id' => 1,
            'title' => 'Software Developer',
            'description' => 'Develop and maintain software applications',
            'requirements' => 'Bachelor degree in Computer Science',
            'org_id' => 1,
            'position_group_id' => 1
        ];

        $positionGroup = [
            'id' => 1,
            'name' => 'IT Positions',
            'exercise_id' => 1
        ];

        $organization = [
            'id' => 1,
            'org_name' => 'Tech Solutions Inc.',
            'description' => 'Leading technology company'
        ];

        $exercise = [
            'id' => 1,
            'title' => 'Annual Recruitment 2024',
            'description' => 'Annual recruitment exercise for various positions'
        ];

        // Get applicant's files
        $files = $this->applicantFilesModel->where('applicant_id', $applicant_id)
                                          ->orderBy('created_at', 'DESC')
                                          ->findAll();

        // Get applicant's experiences
        $experiences = $this->experiencesModel->where('applicant_id', $applicant_id)
                                            ->orderBy('date_from', 'DESC')
                                            ->findAll();

        // Get applicant's education
        $education = $this->applicantEducationModel->where('applicant_id', $applicant_id)
                                                 ->orderBy('date_from', 'DESC')
                                                 ->findAll();

        $totalExperience = 4;
        $highestEducation = 'Bachelor of Computer Science';

        // Prepare data for the view
        $data = [
            'title' => 'Application Details',
            'menu' => 'applications',
            'application' => $application,
            'position' => $position,
            'positionGroup' => $positionGroup,
            'organization' => $organization,
            'exercise' => $exercise,
            'files' => $files,
            'experiences' => $experiences,
            'education' => $education,
            'totalExperience' => $totalExperience,
            'highestEducation' => $highestEducation,
            'educationLevels' => [
                1 => 'Primary',
                2 => 'Secondary',
                3 => 'Certificate',
                4 => 'Diploma',
                5 => 'Bachelor\'s Degree',
                6 => 'Master\'s Degree',
                7 => 'Doctorate'
            ]
        ];

        // Render the view
        return view('applicant/applicant_application_details', $data);
    }

    public function applications()
    {
        $applicant_id = session()->get('applicant_id');
        
        // Using dummy data for UI development
        $applications = [
            [
                'id' => 1,
                'position_title' => 'Software Developer',
                'organization' => 'Tech Solutions Inc.',
                'applied_date' => '2024-01-15',
                'status' => 'pending'
            ],
            [
                'id' => 2,
                'position_title' => 'Project Manager',
                'organization' => 'Business Corp',
                'applied_date' => '2024-01-10',
                'status' => 'shortlisted'
            ]
        ];

        $data = [
            'title' => 'My Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('applicant/applicant_applications', $data);
    }
}
