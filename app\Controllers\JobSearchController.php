<?php

namespace App\Controllers;

use App\Models\PositionsModel;
use App\Models\orgModel;

class JobSearchController extends BaseController
{
    protected $positionsModel;
    protected $orgModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->positionsModel = new PositionsModel();
        $this->orgModel = new orgModel();
    }

    /**
     * Display all published job positions
     */
    public function jobs()
    {
        // Get all published positions using the model method
        $positions = $this->positionsModel->getAllPublishedPositions();

        // Debug: Log the number of positions found
        log_message('debug', 'Found ' . count($positions) . ' positions');

        // Debug: Log each position for inspection
        foreach ($positions as $position) {
            log_message('debug', 'Position ID: ' . $position['id'] .
                        ', Name: ' . $position['designation'] .
                        ', Status: ' . $position['status'] .
                        ', Group ID: ' . $position['position_group_id']);
        }

        // Get organizations for filtering
        $organizations = $this->orgModel->findAll();

        // Extract unique classifications and locations for filtering
        $classifications = array_unique(array_filter(array_column($positions, 'classification')));
        $locations = array_unique(array_filter(array_column($positions, 'location')));

        // Return the view with data
        return view('home/home_jobs', [
            'title' => 'Available Positions',
            'positions' => $positions,
            'organizations' => $organizations,
            'classifications' => $classifications,
            'locations' => $locations
        ]);
    }

    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/jobs');
        }

        // Get position with related data using a single query
        // First, let's try to get the position without the problematic join to debug
        $position = $this->positionsModel->select('
            positions.*,
            dakoii_org.orgcode,
            positions_groups.group_name,
            exercises.exercise_name,
            exercises.status as exercise_status,
            exercises.publish_date_from,
            exercises.publish_date_to
        ')
        ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
        ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
        ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
        ->where('positions.id', $id)
        ->where('(positions.status = 1 OR positions.status = "active")') // Handle both numeric and string status
        ->first();

        if ($position === null) {
            return redirect()->to('/jobs');
        }

        // Get organization details separately to avoid column issues
        if (!empty($position['org_id'])) {
            $org = $this->orgModel->find($position['org_id']);
            if ($org) {
                $position['org_name'] = $org['name'] ?? $org['orgcode'] ?? 'Unknown Organization';
            } else {
                $position['org_name'] = 'Unknown Organization';
            }
        } else {
            $position['org_name'] = 'Unknown Organization';
        }

        return view('home/home_job_details', [
            'position' => $position
        ]);
    }
}
