<?php

namespace App\Controllers;

use App\Models\PositionsModel;
use App\Models\orgModel;

class JobSearchController extends BaseController
{
    protected $positionsModel;
    protected $orgModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->positionsModel = new PositionsModel();
        $this->orgModel = new orgModel();
    }

    /**
     * Display all published job positions
     */
    public function jobs()
    {
        // Using dummy data for UI development - replace with actual model calls later
        $positions = [
            [
                'id' => 1,
                'designation' => 'Senior Software Engineer',
                'org_name' => 'Department of Information Technology',
                'orgcode' => 'DIT',
                'location' => 'Port Moresby, NCD',
                'annual_salary' => 'K85,000 - K95,000',
                'classification' => 'Level 8',
                'position_reference' => 'DIT-SE-2024-001',
                'publish_date_to' => '2024-12-31',
                'status' => 'active'
            ],
            [
                'id' => 2,
                'designation' => 'Project Manager',
                'org_name' => 'Department of Works',
                'orgcode' => 'DOW',
                'location' => 'Lae, Morobe',
                'annual_salary' => 'K75,000 - K85,000',
                'classification' => 'Level 7',
                'position_reference' => 'DOW-PM-2024-002',
                'publish_date_to' => '2024-11-30',
                'status' => 'active'
            ],
            [
                'id' => 3,
                'designation' => 'Financial Analyst',
                'org_name' => 'Department of Treasury',
                'orgcode' => 'DOT',
                'location' => 'Port Moresby, NCD',
                'annual_salary' => 'K65,000 - K75,000',
                'classification' => 'Level 6',
                'position_reference' => 'DOT-FA-2024-003',
                'publish_date_to' => '2024-12-15',
                'status' => 'active'
            ],
            [
                'id' => 4,
                'designation' => 'Human Resources Officer',
                'org_name' => 'Department of Personnel Management',
                'orgcode' => 'DPM',
                'location' => 'Port Moresby, NCD',
                'annual_salary' => 'K55,000 - K65,000',
                'classification' => 'Level 5',
                'position_reference' => 'DPM-HR-2024-004',
                'publish_date_to' => '2024-12-20',
                'status' => 'active'
            ],
            [
                'id' => 5,
                'designation' => 'Education Officer',
                'org_name' => 'Department of Education',
                'orgcode' => 'DOE',
                'location' => 'Mount Hagen, WHP',
                'annual_salary' => 'K50,000 - K60,000',
                'classification' => 'Level 4',
                'position_reference' => 'DOE-EO-2024-005',
                'publish_date_to' => '2024-11-25',
                'status' => 'active'
            ]
        ];

        // Dummy organizations for filtering
        $organizations = [
            ['id' => 1, 'name' => 'Department of Information Technology'],
            ['id' => 2, 'name' => 'Department of Works'],
            ['id' => 3, 'name' => 'Department of Treasury'],
            ['id' => 4, 'name' => 'Department of Personnel Management'],
            ['id' => 5, 'name' => 'Department of Education']
        ];

        // Extract unique classifications and locations for filtering
        $classifications = array_unique(array_filter(array_column($positions, 'classification')));
        $locations = array_unique(array_filter(array_column($positions, 'location')));

        // Return the view with data
        return view('home/home_jobs', [
            'title' => 'Available Positions',
            'menu' => 'jobs',
            'positions' => $positions,
            'organizations' => $organizations,
            'classifications' => $classifications,
            'locations' => $locations
        ]);
    }

    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/jobs');
        }

        // Using dummy data for UI development - replace with actual model calls later
        $positions = [
            1 => [
                'id' => 1,
                'designation' => 'Senior Software Engineer',
                'org_name' => 'Department of Information Technology',
                'orgcode' => 'DIT',
                'location' => 'Port Moresby, NCD',
                'annual_salary' => 'K85,000 - K95,000',
                'classification' => 'Level 8',
                'position_reference' => 'DIT-SE-2024-001',
                'publish_date_from' => '2024-01-15',
                'publish_date_to' => '2024-12-31',
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'qualifications' => 'Bachelor\'s degree in Computer Science, Software Engineering, or related field. Minimum 5 years of experience in software development.',
                'knowledge' => 'Proficiency in multiple programming languages including Java, Python, JavaScript. Experience with cloud platforms (AWS, Azure). Knowledge of database systems and web technologies.',
                'skills_competencies' => 'Strong problem-solving skills, excellent communication abilities, team leadership experience, project management skills.',
                'job_experiences' => 'Minimum 5 years of software development experience. Experience leading development teams. Previous work on large-scale applications.',
                'jd_filepath' => 'public/uploads/job_descriptions/senior_software_engineer.pdf',
                'status' => 'active'
            ],
            2 => [
                'id' => 2,
                'designation' => 'Project Manager',
                'org_name' => 'Department of Works',
                'orgcode' => 'DOW',
                'location' => 'Lae, Morobe',
                'annual_salary' => 'K75,000 - K85,000',
                'classification' => 'Level 7',
                'position_reference' => 'DOW-PM-2024-002',
                'publish_date_from' => '2024-01-20',
                'publish_date_to' => '2024-11-30',
                'exercise_name' => 'Infrastructure Development Exercise',
                'qualifications' => 'Bachelor\'s degree in Engineering, Project Management, or related field. PMP certification preferred.',
                'knowledge' => 'Project management methodologies, construction industry knowledge, budget management, risk assessment.',
                'skills_competencies' => 'Leadership skills, communication, stakeholder management, problem-solving, time management.',
                'job_experiences' => 'Minimum 3 years of project management experience. Experience in infrastructure or construction projects.',
                'jd_filepath' => 'public/uploads/job_descriptions/project_manager.pdf',
                'status' => 'active'
            ],
            3 => [
                'id' => 3,
                'designation' => 'Financial Analyst',
                'org_name' => 'Department of Treasury',
                'orgcode' => 'DOT',
                'location' => 'Port Moresby, NCD',
                'annual_salary' => 'K65,000 - K75,000',
                'classification' => 'Level 6',
                'position_reference' => 'DOT-FA-2024-003',
                'publish_date_from' => '2024-02-01',
                'publish_date_to' => '2024-12-15',
                'exercise_name' => 'Finance Recruitment 2024',
                'qualifications' => 'Bachelor\'s degree in Finance, Accounting, Economics, or related field. CPA qualification preferred.',
                'knowledge' => 'Financial analysis, budgeting, forecasting, financial reporting, government accounting principles.',
                'skills_competencies' => 'Analytical thinking, attention to detail, Excel proficiency, report writing, presentation skills.',
                'job_experiences' => 'Minimum 2 years of financial analysis experience. Government sector experience preferred.',
                'jd_filepath' => 'public/uploads/job_descriptions/financial_analyst.pdf',
                'status' => 'active'
            ]
        ];

        // Get the position by ID
        $position = $positions[$id] ?? null;

        if ($position === null) {
            return redirect()->to('/jobs')->with('error', 'Position not found.');
        }

        return view('home/home_job_details', [
            'title' => $position['designation'],
            'position' => $position
        ]);
    }
}
