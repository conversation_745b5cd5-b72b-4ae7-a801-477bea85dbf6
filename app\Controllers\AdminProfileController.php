<?php

namespace App\Controllers;

use App\Models\APPX_ApplicationInformationModel;
use App\Models\APPX_ApplicationExperiencesModel;
use App\Models\APPX_ApplicationEducationModel;
use App\Models\APPX_ApplicationFilesModel;
use App\Models\ExerciseModel;
use App\Models\PositionsGroupModel;
use App\Models\PositionsModel;

class AdminProfileController extends BaseController
{
    protected $applicationModel;
    protected $exerciseModel;
    protected $positionsGroupModel;
    protected $positionModel;
    protected $experienceModel;
    protected $educationModel;
    protected $filesModel;
    protected $session;

    public function __construct()
    {
        $this->session = \Config\Services::session();
        $this->applicationModel = new APPX_ApplicationInformationModel();
        $this->exerciseModel = new ExerciseModel();
        $this->positionsGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionsModel();
        $this->experienceModel = new APPX_ApplicationExperiencesModel();
        $this->educationModel = new APPX_ApplicationEducationModel();
        $this->filesModel = new APPX_ApplicationFilesModel();
        
        // Load helpers
        helper(['url', 'form', 'GeminiAI']);
    }

    /**
     * Display the list of exercises for profiling.
     */
    public function index()
    {
        // Fetch exercises that are in selection stage or published (to ensure we get exercises to profile)
        $data['exercises'] = $this->exerciseModel->where('status', 'selection')
                                               ->orWhere('status', 'published')
                                               ->orderBy('exercise_name', 'ASC')
                                               ->findAll(); 
        
        // If no exercises found, check all exercises (for debugging)
        if (empty($data['exercises'])) {
            log_message('debug', 'No exercises found with status "selection" or "published". Fetching all exercises for debugging.');
            $data['exercises'] = $this->exerciseModel->findAll();
        }
        
        // For each exercise, count the total position groups
        foreach ($data['exercises'] as &$exercise) {
            $groupCount = $this->positionsGroupModel->where('exercise_id', $exercise['id'])->countAllResults();
            $exercise['group_count'] = $groupCount;
            
            // Count total positions in this exercise's groups
            $positionCount = 0;
            $groups = $this->positionsGroupModel->where('exercise_id', $exercise['id'])->findAll();
            foreach ($groups as $group) {
                $positionCount += $this->positionModel->where('position_group_id', $group['id'])->countAllResults();
            }
            $exercise['position_count'] = $positionCount;
            
            // Count total applicants with 'passed' pre-screening status
            $applicantCount = 0;
            $positions = $this->positionModel->select('id')->where('position_group_id IN (SELECT id FROM positions_groups WHERE exercise_id = '.$exercise['id'].')')->findAll();
            $positionIds = array_column($positions, 'id');
            if (!empty($positionIds)) {
                $applicantCount = $this->applicationModel->whereIn('position_id', $positionIds)
                                     ->where('pre_screened_status', 'passed')
                                     ->countAllResults();
            }
            $exercise['applicant_count'] = $applicantCount;
        }
        
        // Log exercise details for debugging
        foreach ($data['exercises'] as $exercise) {
            log_message('debug', "Exercise ID: {$exercise['id']}, Name: {$exercise['exercise_name']}, Status: {$exercise['status']}");
        }
        
        $data['title'] = 'Profiling Exercises';
        $data['menu'] = 'profiling'; // For active menu highlighting

        return view('profiles/profiles_exercise_list', $data);
    }

    /**
     * Display position groups within a specific exercise.
     * @param int $exerciseId
     */
    public function profilePositionGroups($exerciseId)
    {
        // Fetch position groups for the exercise
        $data['position_groups'] = $this->positionsGroupModel->where('exercise_id', $exerciseId)->findAll();
        $data['exercise'] = $this->exerciseModel->find($exerciseId); // Fetch exercise details
        
        // Ensure exercise name is never empty
        if (empty($data['exercise']) || empty($data['exercise']['exercise_name'])) {
            log_message('error', "Exercise not found or name is empty for ID: $exerciseId");
            $data['exercise'] = ['id' => $exerciseId, 'exercise_name' => 'Exercise ' . $exerciseId];
        }
        
        // Add counts to each position group
        foreach ($data['position_groups'] as &$group) {
            // Count positions in this group
            $positionCount = $this->positionModel->where('position_group_id', $group['id'])->countAllResults();
            $group['position_count'] = $positionCount;
            
            // Count applicants for positions in this group
            $applicantCount = 0;
            $positions = $this->positionModel->select('id')->where('position_group_id', $group['id'])->findAll();
            $positionIds = array_column($positions, 'id');
            if (!empty($positionIds)) {
                $applicantCount = $this->applicationModel->whereIn('position_id', $positionIds)
                                     ->where('pre_screened_status', 'passed')
                                     ->countAllResults();
            }
            $group['applicant_count'] = $applicantCount;
        }
        
        $data['title'] = 'Position Groups for ' . $data['exercise']['exercise_name'];
        $data['menu'] = 'profiling';

        return view('profiles/profiles_position_group_list', $data);
    }

    /**
     * Display positions within a specific position group.
     * @param int $groupId
     */
    public function profilePositions($groupId)
    {
        // Fetch positions for the group
        $data['positions'] = $this->positionModel->where('position_group_id', $groupId)->findAll();
        $data['position_group'] = $this->positionsGroupModel->find($groupId); // Fetch group details

        // Fetch exercise details for breadcrumbs
        if ($data['position_group']) {
            $exercise = $this->exerciseModel->find($data['position_group']['exercise_id']);
            $data['position_group']['exercise_name'] = $exercise['exercise_name'] ?? 'Exercise ' . $data['position_group']['exercise_id'];
        } else {
            $data['position_group'] = ['id' => $groupId, 'group_name' => 'Group ' . $groupId];
        }
        
        // Add applicant counts to each position
        foreach ($data['positions'] as &$position) {
            $applicantCount = $this->applicationModel->where('position_id', $position['id'])
                                ->where('pre_screened_status', 'passed')
                                ->countAllResults();
            $position['applicant_count'] = $applicantCount;
        }
        
        $data['title'] = 'Positions in ' . $data['position_group']['group_name'];
        $data['menu'] = 'profiling';

        return view('profiles/profiles_position_list', $data);
    }

    /**
     * Display applicants for a specific position who passed pre-screening.
     * @param int $positionId
     */
    public function profileApplicants($positionId)
    {
        // Fetch position details
        $data['position'] = $this->positionModel->find($positionId);
        
        // Handle case where position is not found
        if (empty($data['position'])) {
            log_message('error', "Position not found for ID: $positionId");
            $data['position'] = ['id' => $positionId, 'designation' => 'Position ' . $positionId];
        }

        // Fetch group and exercise details for breadcrumbs
        if (isset($data['position']['position_group_id'])) {
            $group = $this->positionsGroupModel->find($data['position']['position_group_id']);
            if ($group) {
                $data['position']['group_name'] = $group['group_name'] ?? 'Group ' . $group['id'];
                $data['position']['position_group_id'] = $group['id']; // Pass group ID
                
                if (isset($group['exercise_id'])) {
                    $exercise = $this->exerciseModel->find($group['exercise_id']);
                    if ($exercise) {
                        $data['position']['exercise_name'] = $exercise['exercise_name'] ?? 'Exercise ' . $exercise['id'];
                        $data['position']['exercise_id'] = $exercise['id']; // Pass exercise ID
                    } else {
                        $data['position']['exercise_name'] = 'Exercise ' . $group['exercise_id'];
                        $data['position']['exercise_id'] = $group['exercise_id'];
                    }
                }
            }
        }

        // Fetch applicants for this position with 'passed' pre_screened_status
        $data['applications'] = $this->applicationModel
            ->where('position_id', $positionId)
            ->where('pre_screened_status', 'passed') // Filter by passed status
            ->findAll();

        $data['title'] = 'Profiling Applications for ' . ($data['position']['designation'] ?? 'Position');
        $data['menu'] = 'profiling';

        return view('profiles/profiles_applicant_list', $data);
    }
}
