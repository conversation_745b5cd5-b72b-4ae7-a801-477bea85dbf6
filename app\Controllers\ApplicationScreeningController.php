<?php

namespace App\Controllers;

use App\Models\APPX_ApplicationInformationModel;
use App\Models\APPX_ApplicationExperiencesModel;
use App\Models\APPX_ApplicationEducationModel;
use App\Models\APPX_ApplicationFilesModel;
use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\PositionsGroupModel;
use App\Models\applicantsModel;

class ApplicationScreeningController extends BaseController
{
    protected $applicationModel;
    protected $applicantsModel;
    protected $experienceModel;
    protected $educationModel;
    protected $filesModel;
    protected $exerciseModel;
    protected $positionModel;
    protected $positionGroupModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = \Config\Services::session();
        $this->applicationModel = new APPX_ApplicationInformationModel();
        $this->applicantsModel = new applicantsModel();
        $this->experienceModel = new APPX_ApplicationExperiencesModel();
        $this->educationModel = new APPX_ApplicationEducationModel();
        $this->filesModel = new APPX_ApplicationFilesModel();
        $this->exerciseModel = new ExerciseModel();
        $this->positionModel = new PositionsModel();
        $this->positionGroupModel = new PositionsGroupModel();
    }

    /**
     * [GET] Display the list of applications pending pre-screening.
     * URI: /application_screening
     */
    public function index()
    {
        // Get all applications where received_acknowledged is not empty and pre_screened is empty/null
        $applications = $this->applicationModel
            ->select('appx_application_information.*, applicants.fname, applicants.lname, positions.designation as position_name')
            ->join('applicants', 'applicants.applicant_id = appx_application_information.applicant_id', 'left')
            ->join('positions', 'positions.id = appx_application_information.position_id', 'left')
            ->where('appx_application_information.recieved_acknowledged IS NOT NULL')
            ->orderBy('appx_application_information.created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('application_pre_screening/application_pre_screening_list', $data);
    }

    /**
     * [GET] Display the detailed view of a specific application for pre-screening.
     * URI: /application_screening/show/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function show($id)
    {
        // Get application with related position and exercise data
        $application = $this->applicationModel
            ->select('
                appx_application_information.*,
                positions.designation,
                positions_groups.group_name, 
                exercises.id as exercise_id, 
                exercises.exercise_name, 
                exercises.pre_screen_criteria
            ')
            ->join('positions', 'positions.id = appx_application_information.position_id', 'left')
            ->join('positions_groups', 'positions_groups.id = positions.position_group_id', 'left')
            ->join('exercises', 'exercises.id = positions_groups.exercise_id', 'left')
            ->where('appx_application_information.id', $id)
            ->first();

        if (!$application) {
            return redirect()->to(base_url('application_screening'))
                ->with('error', 'Application not found.');
        }

        // Fetch the corresponding applicant data
        $applicant = $this->applicantsModel->find($application['applicant_id']);
        if (!$applicant) {
            log_message('warning', 'Applicant record not found for application ID: ' . $id . ' and applicant ID: ' . $application['applicant_id']);
            $applicant = [];
        }

        // Get related data for this application
        $experiences = $this->experienceModel->getExperiencesByApplicationId($id);
        $education = $this->educationModel->getEducationByApplicationId($id);
        $files = $this->filesModel->getFilesByApplicationId($id);
        
        // Get position and exercise data
        $position = [
            'id' => $application['position_id'],
            'designation' => $application['designation'] ?? 'N/A'
        ];
        
        $exercise = [
            'id' => $application['exercise_id'],
            'exercise_name' => $application['exercise_name'] ?? 'N/A'
        ];
        
        // Parse pre-screening criteria
        $preScreenCriteria = [];
        if (!empty($application['pre_screen_criteria'])) {
            try {
                $preScreenCriteria = json_decode($application['pre_screen_criteria'], true) ?? [];
            } catch (\Exception $e) {
                log_message('error', 'Error parsing pre-screening criteria: ' . $e->getMessage());
            }
        }

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'application' => $application,
            'applicant' => $applicant,
            'position' => $position,
            'exercise' => $exercise,
            'preScreenCriteria' => $preScreenCriteria,
            'experiences' => $experiences,
            'education' => $education,
            'files' => $files,
            'educationModel' => $this->educationModel
        ];

        return view('application_pre_screening/application_pre_screening_detailed_view', $data);
    }

    /**
     * [POST] Save pre-screening results for an application.
     * URI: /application_screening/save/{id}
     *
     * @param int $id Application ID
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save($id)
    {
        // Find the application first to ensure it exists
        $application = $this->applicationModel->find($id);
        if (!$application) {
            return redirect()->to(base_url('application_screening'))
                ->with('error', 'Application not found.');
        }

        // Validate input
        $rules = [
            'status' => 'required|in_list[passed,failed,pending]',
            'remarks' => 'permit_empty|string|max_length[1000]'
        ];
        
        if ($this->request->getPost('status') === 'failed' && empty($this->request->getPost('remarks'))) {
            $rules['remarks'] = 'required|string|max_length[1000]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Process criteria results
        $criteriaResults = [];
        $criteriaIndices = $this->request->getPost('criteria_index') ?? [];
        $criteriaMet = $this->request->getPost('criteria_met') ?? [];
        $criteriaRemarks = $this->request->getPost('criteria_remarks') ?? [];
        
        foreach ($criteriaIndices as $index => $criteriaIndex) {
            $criteriaResults[] = [
                'criteriaIndex' => $criteriaIndex,
                'met' => isset($criteriaMet[$criteriaIndex]) ? true : false,
                'remarks' => trim($criteriaRemarks[$criteriaIndex] ?? '')
            ];
        }

        // Prepare data for update
        $data = [
            'pre_screened' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $this->request->getPost('status'),
            'pre_screened_remarks' => trim($this->request->getPost('remarks')),
            'pre_screened_criteria_results' => json_encode($criteriaResults),
            'updated_by' => $this->session->get('user_id')
        ];

        // Update the application
        if ($this->applicationModel->update($id, $data)) {
            return redirect()->to(base_url('application_screening/show/' . $id))
                ->with('success', 'Pre-screening results saved successfully.');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to save pre-screening results: ' . implode(', ', $this->applicationModel->errors()));
        }
    }
}
