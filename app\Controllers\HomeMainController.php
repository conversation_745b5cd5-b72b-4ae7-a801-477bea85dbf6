<?php

namespace App\Controllers;

use App\Models\PositionsModel;
use App\Models\orgModel;

class HomeMainController extends BaseController
{
    protected $positionsModel;
    protected $orgModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->positionsModel = new PositionsModel();
        $this->orgModel = new orgModel();
    }

    public function index()
    {
        // Get random published positions directly linked to exercises
        $positions = $this->positionsModel->getRandomPublishedPositions(10);

        return view('home/home_home', [
            'latest_positions' => $positions
        ]);
    }

    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }
}
