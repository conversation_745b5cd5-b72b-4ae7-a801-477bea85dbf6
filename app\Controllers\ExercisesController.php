<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class ExercisesController extends ResourceController
{
    protected $helpers = ['form', 'url', 'info'];
    protected $session;

    public function __construct()
    {
        $this->session = session();
    }

    public function exercise_management()
    {
        return view('exercises/exercises_exercise_management', [
            'title' => 'Exercise Management',
            'menu' => 'exercises'
        ]);
    }

    public function list()
    {
        $exercises = $this->exerciseModel->where('org_id', session()->get('org_id'))->findAll();
        return $this->response->setJSON(['data' => $exercises]);
    }

    public function create()
    {
        $data = $this->request->getPost();
        $data['created_by'] = session()->get('user_id');
        $data['status'] = 'draft';

        if ($this->exerciseModel->insert($data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Exercise created successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to create exercise'
        ]);
    }

    public function get($id = null)
    {
        $exercise = $this->exerciseModel->find($id);
        return $this->response->setJSON($exercise);
    }

    public function update($id = null)
    {
        $data = $this->request->getPost();
        unset($data['id']);

        $data['updated_by'] = session()->get('user_id');

        if ($this->exerciseModel->update($id, $data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Exercise updated successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to update exercise'
        ]);
    }

    public function delete($id = null)
    {
        // First, check if exercise exists and is in draft status
        $exercise = $this->exerciseModel->find($id);

        if (!$exercise) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise not found'
            ]);
        }

        // Only allow deletion if exercise is in draft status
        if ($exercise['status'] !== 'draft') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Only exercises in draft status can be deleted'
            ]);
        }

        if ($this->exerciseModel->delete($id)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Exercise deleted successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to delete exercise'
        ]);
    }

    /**
     * Change the status of an exercise
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function changeStatus()
    {
        // Get and validate the exercise ID
        $id = $this->request->getPost('id');
        $status = $this->request->getPost('status');

        // Basic validation
        if (empty($id) || !is_numeric($id)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid exercise ID'
            ]);
        }

        // Force ID to be integer
        $id = (int)$id;

        // Debug logging
        log_message('debug', 'Exercise status change request: ID=' . $id . ', Status=' . $status);
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        // Validate the status
        $validStatuses = ['draft', 'publish', 'publish_request', 'selection', 'review', 'closed'];
        if (!in_array($status, $validStatuses)) {
            log_message('debug', 'Invalid status: ' . $status);
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid status value'
            ]);
        }

        // Check if exercise exists
        $exercise = $this->exerciseModel->find($id);
        log_message('debug', 'Exercise lookup result: ' . ($exercise ? 'Found' : 'Not found'));

        if (!$exercise) {
            // Try to get more info about why it's not found
            log_message('debug', 'All exercises: ' . json_encode($this->exerciseModel->findAll(5)));

            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise not found with ID: ' . $id
            ]);
        }

        // Update the exercise status
        $data = [
            'status' => $status,
            'updated_by' => session()->get('user_id') ?? 1 // Fallback to 1 if not set
        ];

        log_message('debug', 'Attempting to update exercise with data: ' . json_encode($data));

        try {
            if ($this->exerciseModel->update($id, $data)) {
                log_message('debug', 'Exercise status update successful for ID: ' . $id);
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Exercise status updated successfully to ' . ucfirst(str_replace('_', ' ', $status))
                ]);
            }

            log_message('debug', 'Exercise status update failed for ID: ' . $id . ' - ' . json_encode($this->exerciseModel->errors()));
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update exercise status: ' . json_encode($this->exerciseModel->errors())
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Exception updating exercise: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating exercise: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display the pre-screening criteria management page for an exercise
     *
     * @param int $id Exercise ID
     * @return string
     */
    public function pre_screen_criteria($id = null)
    {
        // Check if ID is provided
        if (!$id) {
            session()->setFlashdata('error', 'No exercise selected.');
            return redirect()->to(base_url('exercises'));
        }

        // Check if exercise exists
        $exercise = $this->exerciseModel->find($id);
        if (!$exercise) {
            session()->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('exercises'));
        }

        return view('exercises/exercises_pre_screen_criteria', [
            'title' => 'Pre-Screening Criteria',
            'menu' => 'exercises',
            'exercise_id' => $id
        ]);
    }

    /**
     * Save pre-screening criteria for an exercise
     *
     * @param int $id Exercise ID
     * @return \CodeIgniter\HTTP\Response
     */
    public function save_criteria($id = null)
    {
        // Check if exercise exists
        $exercise = $this->exerciseModel->find($id);
        if (!$exercise) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise not found',
                'csrf_hash' => csrf_hash()
            ]);
        }

        // Get pre-screening criteria from request
        $criteria = $this->request->getPost('pre_screen_criteria');

        // Validate JSON format
        if ($criteria) {
            try {
                // Decode and re-encode to ensure valid JSON
                $decodedCriteria = json_decode($criteria);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Invalid criteria format: ' . json_last_error_msg(),
                        'csrf_hash' => csrf_hash()
                    ]);
                }
            } catch (\Exception $e) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid criteria format: ' . $e->getMessage(),
                    'csrf_hash' => csrf_hash()
                ]);
            }
        }

        // Update exercise with pre-screening criteria
        $updateData = [
            'pre_screen_criteria' => $criteria,
            'updated_by' => session()->get('user_id')
        ];

        if ($this->exerciseModel->update($id, $updateData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Pre-screening criteria saved successfully',
                'csrf_hash' => csrf_hash()
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to save pre-screening criteria',
            'csrf_hash' => csrf_hash()
        ]);
    }

    /**
     * Get pre-screening criteria for an exercise
     *
     * @param int $id Exercise ID
     * @return \CodeIgniter\HTTP\Response
     */
    public function get_criteria($id = null)
    {
        // Check if exercise exists
        $exercise = $this->exerciseModel->find($id);
        if (!$exercise) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise not found'
            ]);
        }

        // Get pre-screening criteria
        $criteria = [];
        if (!empty($exercise['pre_screen_criteria'])) {
            try {
                $criteria = json_decode($exercise['pre_screen_criteria'], true);
            } catch (\Exception $e) {
                log_message('error', 'Error decoding criteria JSON: ' . $e->getMessage());
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'criteria' => $criteria
        ]);
    }
}